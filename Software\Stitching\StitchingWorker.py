"""
Stitching Worker <PERSON><PERSON>le
Handles the stitching process using interpolated Z values from Mapping AF
"""

import os
import time
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QImage


class StitchingWorker(QObject):
    """
    Worker class for handling stitching process
    """
    
    # Signals
    progress = pyqtSignal(int, str)  # progress percentage, status message
    log_message = pyqtSignal(str)    # log messages
    finished = pyqtSignal()          # finished signal
    error_occurred = pyqtSignal(str) # error signal
    
    def __init__(self, camera, grbl, z_interpolation, output_folder=None):
        """
        Initialize StitchingWorker
        
        Args:
            camera: Camera instance for capturing images
            grbl: GRBL instance for movement control
            z_interpolation: ZInterpolation object with interpolated grid points
            output_folder: Optional custom output folder path
        """
        super().__init__()
        
        self.camera = camera
        self.grbl = grbl
        self.z_interpolation = z_interpolation
        self.output_folder = output_folder
        
        # Stitching parameters
        self.current_point = 0
        self.total_points = 0
        self.grid_points = []
        self.is_running = False
        self.should_stop = False
        self.last_known_position = (0, 0, 0)
        self.timing_stats = {}
        
        # Movement parameters
        self.movement_feedrate = 1000  # mm/min
        self.z_feedrate = 500         # mm/min for Z movements
        self.settle_time = 0.05       # seconds to wait after movement
        self.capture_delay = 0.01     # seconds to wait before capture
        
    def prepare_stitching(self):
        """
        Prepare stitching by getting all interpolated grid points and creating output folder

        Returns:
            bool: True if preparation successful, False otherwise
        """
        try:
            # Safety check: Validate camera connection
            if self.camera is None:
                self.error_occurred.emit("Camera not available")
                return False

            if not hasattr(self.camera, 'is_running') or not self.camera.is_running:
                self.error_occurred.emit("Camera is not running")
                return False

            # Safety check: Validate GRBL connection
            if self.grbl is None:
                self.error_occurred.emit("GRBL not available")
                return False

            if not hasattr(self.grbl, 'grbl') or not self.grbl.grbl.is_open:
                self.error_occurred.emit("GRBL is not connected")
                return False

            # Get all interpolated points
            if self.z_interpolation is None:
                self.error_occurred.emit("Z interpolation data not available")
                return False

            all_points = self.z_interpolation.get_interpolated_grid_points()
            if not all_points:
                self.error_occurred.emit("No interpolated points available")
                return False

            # Safety check: Validate interpolated points
            if len(all_points) < 4:
                self.error_occurred.emit(f"Too few interpolated points ({len(all_points)}). Need at least 4 points.")
                return False

            # Convert to list and sort for efficient movement pattern
            self.grid_points = self._create_efficient_path(all_points)
            self.total_points = len(self.grid_points)

            # Safety check: Validate coordinate ranges
            if not self._validate_coordinate_ranges():
                return False

            # Validate path distances once at the beginning
            self._validate_path_distances()

            self.log_message.emit(f"Prepared {self.total_points} points for stitching")

            # Create output folder
            if not self._create_output_folder():
                return False

            return True

        except Exception as e:
            self.error_occurred.emit(f"Error preparing stitching: {e}")
            return False
    
    def _create_efficient_path(self, points_dict):
        """
        Create an efficient movement path through all grid points
        Uses zigzag pattern to minimize travel distance
        
        Args:
            points_dict: Dictionary with (x,y) keys and z values
            
        Returns:
            list: List of (x, y, z) tuples in optimal order
        """
        # Convert to list of tuples
        points_list = [(x, y, z) for (x, y), z in points_dict.items()]
        
        # Group by Y coordinate and sort
        y_groups = {}
        for x, y, z in points_list:
            if y not in y_groups:
                y_groups[y] = []
            y_groups[y].append((x, y, z))
        
        # Sort Y coordinates
        sorted_y = sorted(y_groups.keys())
        
        # Create zigzag pattern
        efficient_path = []
        reverse = False
        
        for y in sorted_y:
            row_points = sorted(y_groups[y], key=lambda p: p[0])  # Sort by X
            
            if reverse:
                row_points.reverse()
            
            efficient_path.extend(row_points)
            reverse = not reverse  # Alternate direction for next row
        
        self.log_message.emit(f"Created efficient zigzag path with {len(efficient_path)} points")
        return efficient_path

    def _validate_coordinate_ranges(self):
        """
        Validate that all coordinates are within safe ranges

        Returns:
            bool: True if all coordinates are safe, False otherwise
        """
        try:
            # Define safety limits (adjust based on your machine)
            X_MIN, X_MAX = -5.0, 50.0   # mm
            Y_MIN, Y_MAX = -5.0, 50.0   # mm
            Z_MIN, Z_MAX = 0.0, 25.0    # mm

            for x, y, z in self.grid_points:
                if not (X_MIN <= x <= X_MAX):
                    self.error_occurred.emit(f"X coordinate {x:.3f} is outside safe range [{X_MIN}, {X_MAX}]")
                    return False

                if not (Y_MIN <= y <= Y_MAX):
                    self.error_occurred.emit(f"Y coordinate {y:.3f} is outside safe range [{Y_MIN}, {Y_MAX}]")
                    return False

                if not (Z_MIN <= z <= Z_MAX):
                    self.error_occurred.emit(f"Z coordinate {z:.4f} is outside safe range [{Z_MIN}, {Z_MAX}]")
                    return False

            self.log_message.emit("All coordinates are within safe ranges")
            return True

        except Exception as e:
            self.error_occurred.emit(f"Error validating coordinates: {e}")
            return False

    def _validate_path_distances(self):
        """
        Validate that the distance between consecutive points is within a safe limit.
        This is a check on the generated path, not real-time movement.
        """
        max_step_distance = 10.0  # mm
        if len(self.grid_points) < 2:
            return  # Not enough points to check distance

        for i in range(len(self.grid_points) - 1):
            p1 = self.grid_points[i]
            p2 = self.grid_points[i+1]
            
            distance = ((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)**0.5
            
            if distance > max_step_distance:
                self.log_message.emit(
                    f"Warning: Large distance between point {i} ({p1[0]:.3f}, {p1[1]:.3f}) "
                    f"and point {i+1} ({p2[0]:.3f}, {p2[1]:.3f}). Distance: {distance:.3f}mm"
                )
        self.log_message.emit("Path distance validation complete.")

    def _create_output_folder(self):
        """
        Create output folder for stitching images
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.output_folder is None:
                # Create folder with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                self.output_folder = os.path.join("Software", "Stitching", f"stitching_{timestamp}")
            
            os.makedirs(self.output_folder, exist_ok=True)
            self.log_message.emit(f"Output folder created: {self.output_folder}")
            return True
            
        except Exception as e:
            self.error_occurred.emit(f"Error creating output folder: {e}")
            return False
    
    def run(self):
        """
        Main stitching process
        """
        try:
            self.is_running = True
            self.should_stop = False
            self.timing_stats = {
                'start_time': time.time(),
                'preparation_time': 0,
                'total_movement_time': 0,
                'total_capture_time': 0,
                'total_time': 0
            }
            
            self.log_message.emit("Starting stitching process...")
            
            # Prepare stitching
            prep_start_time = time.time()
            if not self.prepare_stitching():
                self.finished.emit()
                return
            self.timing_stats['preparation_time'] = time.time() - prep_start_time
            
            # Disable GRBL polling for stable operation
            if hasattr(self.grbl, 'stop_polling'):
                self.log_message.emit("[STITCHING] Disabling GRBL polling for stable operation")
                self.grbl.stop_polling()

            # Process each point
            for i, (x, y, z) in enumerate(self.grid_points):
                if not self._validate_system_state():
                    break

                self.current_point = i
                progress_percent = int((i / self.total_points) * 100)
                self.progress.emit(progress_percent, f"Processing point {i+1}/{self.total_points}")
                self.log_message.emit(f"Moving to point {i+1}/{self.total_points}: X={x:.3f}, Y={y:.3f}, Z={z:.4f}")

                # Move to position
                move_start_time = time.time()
                if not self._move_to_position(x, y, z):
                    self.error_occurred.emit(f"Failed to move to position ({x:.3f}, {y:.3f}, {z:.4f})")
                    self._emergency_stop()
                    break
                self.timing_stats['total_movement_time'] += time.time() - move_start_time

                if not self._validate_system_state():
                    break

                # Capture and save image
                capture_start_time = time.time()
                if not self._capture_and_save_image(x, y, z, i):
                    self.error_occurred.emit(f"Failed to capture image at position ({x:.3f}, {y:.3f}, {z:.4f})")
                    continue
                self.timing_stats['total_capture_time'] += time.time() - capture_start_time
            
            if not self.should_stop:
                self.progress.emit(100, "Stitching completed successfully")
                self.log_message.emit("Stitching process completed successfully")
            
        except Exception as e:
            self.error_occurred.emit(f"Error during stitching: {e}")
        finally:
            self.timing_stats['total_time'] = time.time() - self.timing_stats['start_time']
            self._log_timing_summary()
            
            # Re-enable GRBL polling
            if hasattr(self.grbl, 'start_polling'):
                self.log_message.emit("[STITCHING] Re-enabling GRBL polling")
                self.grbl.start_polling()

            self.is_running = False
            self.finished.emit()
    
    def _move_to_position(self, x, y, z):
        """
        Move to specified XYZ position with safety checks

        Args:
            x, y, z: Target coordinates

        Returns:
            bool: True if movement successful
        """
        try:
            # Safety check: Verify GRBL is still connected
            if not self.grbl.grbl.is_open:
                self.error_occurred.emit("GRBL connection lost during movement")
                return False

            # Move XY first with timeout
            self.log_message.emit(f"Moving XY to ({x:.3f}, {y:.3f})")
            self.grbl.move_to(x, y)

            if not self.grbl.wait_for_idle(timeout_seconds=30):
                self.error_occurred.emit("XY movement timeout - stopping for safety")
                return False

            # Verify XY position reached (optional for testing)
            try:
                new_x, new_y, _ = self.grbl.get_current_position()
                xy_tolerance = 0.5  # mm - more lenient tolerance
                distance_error = ((new_x - x)**2 + (new_y - y)**2)**0.5

                if distance_error > xy_tolerance:
                    self.log_message.emit(f"XY position warning: Expected ({x:.3f}, {y:.3f}), got ({new_x:.3f}, {new_y:.3f}), error: {distance_error:.3f}mm")
                    # Don't fail for small position errors in testing
                else:
                    self.log_message.emit(f"XY position verified: ({new_x:.3f}, {new_y:.3f})")
            except Exception as e:
                self.log_message.emit(f"Warning: Could not verify XY position: {e}")

            # Move Z with timeout
            self.log_message.emit(f"Moving Z to {z:.4f}")
            self.grbl.move_to_z(z)

            if not self.grbl.wait_for_idle(timeout_seconds=15):
                self.error_occurred.emit("Z movement timeout - stopping for safety")
                return False

            # Verify Z position reached (optional for testing)
            try:
                _, _, new_z = self.grbl.get_current_position()
                z_tolerance = 0.1  # mm - more lenient tolerance
                z_error = abs(new_z - z)

                if z_error > z_tolerance:
                    self.log_message.emit(f"Z position warning: Expected {z:.4f}, got {new_z:.4f}, error: {z_error:.4f}mm")
                    # Don't fail for small position errors in testing
                else:
                    self.log_message.emit(f"Z position verified: {new_z:.4f}")
            except Exception as e:
                self.log_message.emit(f"Warning: Could not verify Z position: {e}")

            # Wait for settling
            time.sleep(self.settle_time)

            self.log_message.emit(f"Successfully moved to ({x:.3f}, {y:.3f}, {z:.4f})")
            self.last_known_position = (x, y, z)
            return True

        except Exception as e:
            self.error_occurred.emit(f"Critical error during movement: {e}")
            return False
    
    def _capture_and_save_image(self, x, y, z, point_index):
        """
        Capture image from camera and save with coordinate-based filename

        Args:
            x, y, z: Current coordinates
            point_index: Index of current point

        Returns:
            bool: True if capture successful
        """
        try:
            # Safety check: Verify camera is still running
            if not hasattr(self.camera, 'is_running') or not self.camera.is_running:
                self.error_occurred.emit("Camera is not running during capture")
                return False

            # Wait before capture for image stabilization
            time.sleep(self.capture_delay)

            # Get frame from camera
            frame = None
            if hasattr(self.camera, '_last_numpy_frame') and self.camera._last_numpy_frame is not None:
                frame = self.camera._last_numpy_frame.copy()
            else:
                self.log_message.emit("Waiting for camera frame...")
                time.sleep(0.1)  # Short wait if frame not immediately available
                if hasattr(self.camera, '_last_numpy_frame') and self.camera._last_numpy_frame is not None:
                    frame = self.camera._last_numpy_frame.copy()

            if frame is None:
                self.error_occurred.emit("No frame available from camera")
                return False

            # Validate frame dimensions
            if len(frame.shape) != 3:
                self.error_occurred.emit(f"Invalid frame shape: {frame.shape}")
                return False

            height, width, channels = frame.shape
            if channels != 3:
                self.error_occurred.emit(f"Expected 3 channels, got {channels}")
                return False

            # Convert to QImage and save
            bytes_per_line = 3 * width
            q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888)

            if q_image.isNull():
                self.error_occurred.emit("Failed to create QImage from frame")
                return False

            # Create filename with coordinates and timestamp
            timestamp = datetime.now().strftime("%H%M%S")
            filename = f"stitch_p{point_index:03d}_X{x:.3f}_Y{y:.3f}_Z{z:.4f}_{timestamp}.png"
            filepath = os.path.join(self.output_folder, filename)

            # Ensure output directory exists
            os.makedirs(os.path.dirname(filepath), exist_ok=True)

            # Save image with error checking
            if q_image.save(filepath, "PNG"):
                # Verify file was actually created and has reasonable size
                if os.path.exists(filepath) and os.path.getsize(filepath) > 1000:  # At least 1KB
                    self.log_message.emit(f"✓ Saved: {filename} ({os.path.getsize(filepath)} bytes)")
                    return True
                else:
                    self.error_occurred.emit(f"File saved but appears corrupted: {filename}")
                    return False
            else:
                self.error_occurred.emit(f"Failed to save image: {filename}")
                return False

        except Exception as e:
            self.error_occurred.emit(f"Critical error capturing image: {e}")
            return False
    
    def stop(self):
        """
        Stop the stitching process safely
        """
        self.should_stop = True
        self.log_message.emit("Emergency stop requested - stopping stitching process...")

        # Emergency stop GRBL movement
        try:
            if self.grbl and hasattr(self.grbl, 'stop_jog'):
                self.grbl.stop_jog()
                self.log_message.emit("GRBL movement stopped")
        except Exception as e:
            self.log_message.emit(f"Error stopping GRBL: {e}")

    def _emergency_stop(self):
        """
        Emergency stop function for critical situations
        """
        try:
            self.should_stop = True
            self.error_occurred.emit("EMERGENCY STOP ACTIVATED")

            # Stop all GRBL movement immediately
            if self.grbl and hasattr(self.grbl, 'grbl') and self.grbl.grbl.is_open:
                # Send emergency stop command
                self.grbl.grbl.write(b'\x85')  # Real-time command for jog cancel
                self.grbl.grbl.write(b'!')     # Feed hold
                self.log_message.emit("Emergency stop commands sent to GRBL")

        except Exception as e:
            self.log_message.emit(f"Error during emergency stop: {e}")

    def _validate_system_state(self):
        """
        Validate system state before continuing with next point

        Returns:
            bool: True if system is in good state, False otherwise
        """
        try:
            # Check GRBL connection
            if not self.grbl.grbl.is_open:
                self.error_occurred.emit("GRBL connection lost")
                return False

            # Check camera state
            if not self.camera.is_running:
                self.error_occurred.emit("Camera stopped running")
                return False

            # Check if stop was requested
            if self.should_stop:
                self.log_message.emit("Stop requested by user")
                return False

            return True

        except Exception as e:
            self.error_occurred.emit(f"Error validating system state: {e}")
            return False

    def _log_timing_summary(self):
        """
        Log a summary of the timing statistics for the stitching process.
        """
        self.log_message.emit("--- Stitching Timing Summary ---")
        self.log_message.emit(f"Preparation Time: {self.timing_stats.get('preparation_time', 0):.2f} seconds")
        self.log_message.emit(f"Total Movement Time: {self.timing_stats.get('total_movement_time', 0):.2f} seconds")
        self.log_message.emit(f"Total Capture Time: {self.timing_stats.get('total_capture_time', 0):.2f} seconds")
        self.log_message.emit(f"Total Stitching Time: {self.timing_stats.get('total_time', 0):.2f} seconds")
        self.log_message.emit("---------------------------------")
