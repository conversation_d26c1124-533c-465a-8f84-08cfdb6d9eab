import cv2
import numpy as np
import os
import tkinter as tk
from tkinter import filedialog
import pandas as pd

def sobel_focus_score(image_path):
    """Hitung fokus score menggunakan Sobel operator"""
    img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    if img is None:
        return None
    
    sobelx = cv2.Sobel(img, cv2.CV_64F, 1, 0, ksize=3)
    sobely = cv2.Sobel(img, cv2.CV_64F, 0, 1, ksize=3)
    sobel = np.sqrt(sobelx**2 + sobely**2)
    return sobel.var()  # variansi gradien sebagai score ketajaman

# === PILIH FOLDER ===
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Pilih Folder Gambar")

if not folder_path:
    print("Tidak ada folder dipilih.")
    exit()

# === BACA FILE DALAM FOLDER ===
files = [f for f in os.listdir(folder_path) if f.lower().endswith(('.png','.jpg','.jpeg','.bmp','.tif','.tiff'))]

# Pisahkan kategori af dan interp
af_files = [f for f in files if "af" in f.lower()]
interp_files = [f for f in files if "interp" in f.lower()]

results = []

# Bandingkan per pasangan
for af_file in af_files:
    # Ekstrak kunci unik dari nama file AF (contoh: p001_X12.34_Y56.78)
    parts = af_file.split('_')
    if len(parts) < 4:
        continue # Lewati file dengan format nama yang tidak valid
    base_name = f"{parts[0]}_{parts[1]}_{parts[2]}" # Kunci: pXXX_X..._Y...
    
    # Cari pasangan interp yang cocok
    interp_match = next((f for f in interp_files if f.startswith(base_name)), None)
    
    if interp_match:
        af_path = os.path.join(folder_path, af_file)
        interp_path = os.path.join(folder_path, interp_match)
        
        af_score = sobel_focus_score(af_path)
        interp_score = sobel_focus_score(interp_path)
        
        if af_score is None or interp_score is None:
            continue
        
        winner = "af" if af_score > interp_score else "interp"
        
        results.append({
            "Nama": base_name,
            "AF Score": af_score,
            "Interp Score": interp_score,
            "Lebih Tajam": winner
        })

# Buat DataFrame
df = pd.DataFrame(results)

# Simpan ke Excel/CSV
output_path = os.path.join(folder_path, "laporan_perbandingan.csv")
df.to_csv(output_path, index=False)

# Analisis akhir
if not df.empty:
    af_win = (df["Lebih Tajam"] == "af").sum()
    interp_win = (df["Lebih Tajam"] == "interp").sum()
    avg_af = df["AF Score"].mean()
    avg_interp = df["Interp Score"].mean()

    print("=== HASIL ANALISIS ===")
    print(f"Total pasangan dibandingkan : {len(df)}")
    print(f"AF lebih tajam   : {af_win} kali")
    print(f"Interp lebih tajam: {interp_win} kali")
    print(f"Rata-rata skor AF   : {avg_af:.2f}")
    print(f"Rata-rata skor Interp: {avg_interp:.2f}")
else:
    print("=== TIDAK ADA DATA UNTUK DIANALISIS ===")
    print("Tidak ada pasangan file 'af' dan 'interp' yang cocok ditemukan.")
print(f"Laporan detail disimpan di: {output_path}")
